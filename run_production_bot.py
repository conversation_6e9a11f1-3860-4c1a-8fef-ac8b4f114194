#!/usr/bin/env python3
"""
EduGuideBot Production Runner
Simple, reliable production bot runner
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import bot modules
from src.bot.handlers import *
from src.bot.commands import start_command, help_command, settings_command

# Configure logging for production
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


async def callback_router(update, context):
    """Route callback queries to appropriate handlers"""
    query = update.callback_query
    callback_data = query.data
    
    try:
        # Route to appropriate handler based on callback data
        if callback_data == "START_QUIZ":
            await start_quiz_callback(update, context)
        elif callback_data == "HOME":
            await home_screen_callback(update, context)
        elif callback_data.startswith("QS_"):
            await quick_start_callback(update, context)
        elif callback_data.startswith("DET_"):
            await detail_callback(update, context)
        elif callback_data.startswith("CMP_"):
            await compare_callback(update, context)
        elif callback_data.startswith("SAVE_"):
            await save_callback(update, context)
        elif callback_data.startswith("REMOVE_"):
            await remove_callback(update, context)
        elif callback_data.startswith("answer_"):
            await handle_question_answer(update, context)
        elif callback_data.startswith("SEC_"):
            await section_callback(update, context)
        elif callback_data.startswith("MORE_"):
            await more_info_callback(update, context)
        elif callback_data.startswith("TREND_"):
            await trending_callback(update, context)
        elif callback_data.startswith("WIZ_"):
            await wizard_start_callback(update, context)
        elif callback_data == "BACK":
            await back_callback(update, context)
        elif callback_data == "REFRESH":
            await refresh_callback(update, context)
        elif callback_data == "RESTART":
            await restart_callback(update, context)
        elif callback_data.startswith("FB_"):
            await feedback_callback(update, context)
        elif callback_data == "BROWSE_MAJORS":
            await browse_majors_callback(update, context)
        elif callback_data == "SHORTLIST_VIEW":
            await shortlist_callback(update, context)
        elif callback_data == "HELP_INFO":
            await help_callback(update, context)
        elif callback_data == "LANG_TOGGLE":
            await language_toggle_callback(update, context)
        elif callback_data.startswith("FILTERS"):
            await filters_callback(update, context)
        elif callback_data.startswith("FILTER_"):
            await filters_callback(update, context)
        elif callback_data.startswith("APPLY_FILTER_"):
            await apply_filter_callback(update, context)
        else:
            await unknown_callback(update, context)
            
    except Exception as e:
        logger.error(f"Error in callback_router for {callback_data}: {e}", exc_info=True)
        await query.answer("❌ An error occurred. Please try again.")


async def error_handler(update, context):
    """Handle errors"""
    logger.error(f"Update {update} caused error {context.error}", exc_info=True)


async def handle_text_message(update, context):
    """Handle text messages"""
    message_text = update.message.text.lower()
    
    if message_text in ['/start', 'start', 'begin']:
        await start_command(update, context)
    elif message_text in ['/help', 'help']:
        await help_command(update, context)
    elif message_text in ['/settings', 'settings']:
        await settings_command(update, context)
    else:
        await update.message.reply_text(
            "🤖 Please use the buttons to interact with me, or type /start to begin!\n\n"
            "🔘 Available commands:\n"
            "• /start - Start the bot\n"
            "• /help - Get help\n"
            "• /settings - Bot settings"
        )


def main():
    """Main function to run the production bot"""
    print("🚀 STARTING EDUGUIDEBOT PRODUCTION")
    print("=" * 50)
    
    # Get bot token
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        print("❌ BOT_TOKEN not found in environment variables")
        return 1
    
    print(f"🤖 Bot Token: {bot_token[:10]}...{bot_token[-5:]}")
    
    # Create application
    application = Application.builder().token(bot_token).build()
    
    # Add handlers
    print("⚙️ Setting up handlers...")
    
    # Command handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("settings", settings_command))
    
    # Callback query handler
    application.add_handler(CallbackQueryHandler(callback_router))
    
    # Message handler
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text_message))
    
    # Error handler
    application.add_error_handler(error_handler)
    
    print("✅ All handlers registered")
    
    # Start the bot
    print("🔄 Starting bot polling...")
    print("🎉 BOT IS NOW LIVE!")
    print("✅ Ready to receive messages and button clicks")
    print("🔗 Users can interact with @teslajds1bot on Telegram")
    print("Press Ctrl+C to stop the bot")
    print("=" * 50)
    
    try:
        # Run the bot
        application.run_polling(
            poll_interval=1.0,
            timeout=10,
            read_timeout=10,
            write_timeout=10,
            connect_timeout=10,
            drop_pending_updates=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Bot failed: {e}")
        logger.error(f"Bot failed: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
